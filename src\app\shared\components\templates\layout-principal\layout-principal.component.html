<div class="layout-principal">
  <!-- <PERSON><PERSON> (desktop apenas) -->
  @if (!isMobile()) {
    <app-menu-lateral class="menu-lateral-container"></app-menu-lateral>
  }

  <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
  <div class="conteudo-principal">
    
    <!-- Header com controles -->
    <div class="header-controles">
      <div class="flex align-items-center justify-content-between p-3 bg-white border-bottom-1 surface-border">
        <div class="flex align-items-center gap-3">
          <div class="header-title-section">
            <h4 class="m-0 text-green-700">
              <i class="pi pi-map mr-2"></i>
              Mapa de Equipamentos
            </h4>

            @if (equipamentos().length > 0) {
              <span class="text-sm text-muted">
                {{ equipamentos().length }} equipamento(s) encontrado(s)
              </span>
            }

            <!-- Informações do usuário e filial -->
            <!-- <app-info-usuario-filial></app-info-usuario-filial> -->
          </div>
        </div>

        <div class="botoes-header-container">
          <p-button
            icon="pi pi-refresh"
            [outlined]="true"
            size="small"
            pTooltip="Recarregar dados"
            tooltipPosition="bottom"
            [loading]="loading()"
            (onClick)="reloadData()">
          </p-button>

          <p-button
            icon="pi pi-compass"
            size="small"
            label="Equipamentos sem geolocalização"
            pTooltip="Equipamentos sem geolocalização"
            tooltipPosition="bottom"
            severity="contrast"
            (onClick)="openModalSemGeolocalizacao()">
          </p-button>

          @if (!isMobile()) {
            <p-button
              [icon]="filtrosVisible() ? 'pi pi-times' : 'pi pi-filter'"
              [label]="isMobile() ? '' : 'Filtros'"
              size="small"
              pTooltip="Filtros"
              tooltipPosition="bottom"
              [severity]="filtrosVisible() ? 'secondary' : 'contrast'"
              (onClick)="toggleFiltros()">
            </p-button>
          }
        </div>
      </div>
    </div>

    <!-- Área de conteúdo com mapa e filtros -->
    <div class="area-conteudo">
      
      <!-- Área do mapa -->
      <div class="mapa-container">
        
        <!-- Loading overlay -->
        @if (loading()) {
          <div class="loading-overlay">
            <div class="loading-content">
              <p-progressSpinner 
                [style]="{ width: '50px', height: '50px' }"
                strokeWidth="4">
              </p-progressSpinner>
              <p class="mt-3 text-center">Carregando equipamentos...</p>
            </div>
          </div>
        }

        <!-- Error message -->
        @if (error()) {
          <div class="error-overlay">
            <p-message 
              severity="error" 
              [text]="error()!"
              [closable]="true"
              (onClose)="error.set(null)">
            </p-message>
          </div>
        }

        <!-- Componente do mapa -->
        <app-mapa-google
          [equipamentos]="equipamentos()"
          class="w-full h-full"
          #mapaGoogle>
        </app-mapa-google>

        <!-- Info panel (quando não há equipamentos) -->
        @if (!loading() && equipamentos().length === 0 && !error()) {
          <div class="no-data-overlay">
            <div class="no-data-content text-center">
              <i class="pi pi-info-circle text-6xl text-muted mb-3"></i>
              <h5 class="text-muted">Nenhum equipamento encontrado</h5>
              <p class="text-sm text-muted">
                Ajuste os filtros para encontrar equipamentos ou verifique se há dados disponíveis.
              </p>
              <p-button
                label="Limpar Filtros"
                icon="pi pi-filter-slash"
                [outlined]="true"
                size="small"
                (onClick)="clearFilters()">
              </p-button>
            </div>
          </div>
        }
      </div>

      <!-- Painel de filtros (desktop) -->
      @if (!isMobile()) {
        <div class="filtros-panel" [class.visible]="filtrosVisible()">
          <app-painel-filtros></app-painel-filtros>
        </div>
      }
    </div>
  </div>
</div>

<!-- Rodapé de Ações (mobile) -->
 @if (isMobile()) {
  <div class="footer-mobile-actions">
    <button class="menu-button" (click)="toggleMenuMobile()">
      <i class="pi pi-bars"></i>
      MENU
    </button>
    <button class="filtros-button" (click)="toggleFiltros()">
      <i class="pi pi-filter"></i>
      FILTROS
    </button>
  </div>
}

  <app-sidebar [title]="'Menu'" [visible]="menuMobileVisible()" (close)="menuMobileVisible.set(false)">
    <app-menu-mobile></app-menu-mobile>
  </app-sidebar>


@if (isMobile()) {
  <app-sidebar [title]="'Filtros'" [visible]="filtrosVisible()" (close)="painelFiltros.onSidebarCloseAttempt()">
    <app-painel-filtros #painelFiltros (closeSidebarEvent)="closeMobileFiltersPanel()"></app-painel-filtros>
  </app-sidebar>
}



