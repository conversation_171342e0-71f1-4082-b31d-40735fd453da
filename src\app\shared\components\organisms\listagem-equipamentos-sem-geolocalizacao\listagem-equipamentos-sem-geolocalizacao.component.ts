import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnInit } from '@angular/core';
import { EquipamentoResponse } from '@core/models/equipamento.model';
import { EquipamentoService } from '@core/services/equipamento.service';
import { BadgeModule } from 'primeng/badge';
import { CardModule } from 'primeng/card';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';

@Component({
  selector: 'app-listagem-equipamentos-sem-geolocalizacao',
  imports: [TableModule, TagModule, BadgeModule, CardModule],
  standalone: true,
  styleUrls: ['./listagem-equipamentos-sem-geolocalizacao.component.scss'],
  templateUrl: './listagem-equipamentos-sem-geolocalizacao.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListagemEquipamentosSemGeolocalizacaoComponent implements OnInit {
  equipamentos: EquipamentoResponse[] = []
  private equipamentoService = inject(EquipamentoService)
  private cdr = inject(ChangeDetectorRef)
  loading = false
  ngOnInit(): void {
    this.loading = true
    this.equipamentoService.findEquipmentWithoutGeolocalization().subscribe(equipamentos => {
      this.equipamentos = equipamentos
      this.loading = false
      this.cdr.markForCheck()
    })
  }

}
