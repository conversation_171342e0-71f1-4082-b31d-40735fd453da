export interface EquipamentoResponse {
  tipoEquipamento: string;
  nred: string;
  latitude: string;
  longitude: string;
  endereco: string;
  bairro: string;
  cidade: string;
  estado: string;
  cep: string;
  nome: string;
  tempoDias: number;
  dataUltMovimento: string;
  id: string;
  situacao: string;
  codFil: number;
  limite: number;
  limiteLocal: number;
  solicitante: string;
  nredFat: string;
  dataPrevisaoColeta: string;
  dataEntrega: string;
}

export interface EquipamentoFiltro {
  codFil?: number;
  tipoEquip?: string[];
  nred?: string[];
  bairro?: string[];
  nredFat?: string[];
  solicitante?: string[];
  ids?: string[];
}

export interface TipoEquipamento {
  codigo: string;
  descricao: string;
  icone: string;
}

export const TIPOS_EQUIPAMENTO: TipoEquipamento[] = [
  {
    codigo: 'CP',
    descricao: 'Container Padrão',
    icone: 'cp_verde.svg'
  },
  {
    codigo: 'CO',
    descricao: 'Container Orgânico',
    icone: 'co_verde.png'
  },
  {
    codigo: 'LX',
    descricao: 'Lixeira',
    icone: 'lx_verde.png'
  },
  {
    codigo: 'CR',
    descricao: 'Container Rodinha',
    icone: 'cr_verde.png'
  },
  {
    codigo: 'CA',
    descricao: 'Container Almoxarifado',
    icone: 'ca_verde.png'
  },
  {
    codigo: 'TR',
    descricao: 'Totem Remédio',
    icone: 'tr_verde.png'
  }
];
