<nav class="menu-mobile">
  <!-- <PERSON><PERSON> -->
  @for (section of menuItems; track section) {
    <div class="menu-section">

      <!-- <PERSON><PERSON><PERSON><PERSON> <PERSON> -->
      <div class="section-header">
        <i class="{{ section.icon }}"></i>
        <span>{{ section.label }}</span>
      </div>

      <!-- Itens -->
       @if (section.items) {
         <div>
           @for (item of section.items; track item) {
             <button
               [disabled]="item.disabled"
               class="menu-item mb-2"
               [class.active]="isActive(item.routerLink)"
               (click)="navigate(item.routerLink)">
               <i class="{{ item.icon }}"></i>
               <span>{{ item.label }}</span>
             </button>
           }
         </div>
       } @else {
         <button
           [disabled]="section.disabled"
           class="menu-item"
           [class.active]="isActive(section.routerLink)"
           (click)="navigate(section.routerLink)">
           <i class="{{ section.icon }}"></i>
           <span>{{ section.label }}</span>
         </button>
       }
    </div>
  }

  <!-- <PERSON><PERSON> do Usuário (no final) -->
  <div class="user-section">
    @if (usuario()) {
      <div class="user-info">
        <p-avatar
          image="assets/images/ecoavatar.png"
          size="large"
          shape="circle"
          [style]="{ 'background-color': 'var(--green-700)', 'color': 'white' }">
        </p-avatar>
        <div class="user-details">
          <span class="user-name">{{ usuario()?.nome }}</span>
          <span class="user-email">{{ usuario()?.email }}</span>
          @if (usuario()?.filialAtual) {
            <div class="filial-info">
              <span class="filial-name">{{ usuario()?.filialAtual?.codigoFormatado }} - {{ usuario()?.filialAtual?.nome }}</span>
              <span class="filial-address">{{ usuario()?.filialAtual?.cidade }}/{{ usuario()?.filialAtual?.uf }}</span>
            </div>
          }
        </div>
      </div>
      <div class="user-actions">
        <p-button
          icon="pi pi-sign-out"
          label="Sair"
          severity="secondary"
          size="small"
          styleClass="w-full logout-btn"
          (onClick)="logout()">
        </p-button>
      </div>
    } @else {
      <div class="user-loading">
        <p-avatar
          icon="pi pi-user"
          size="large"
          shape="circle"
          [style]="{ 'background-color': 'var(--green-700)', 'color': 'white' }">
        </p-avatar>
        <span>Carregando...</span>
      </div>
    }
  </div>
</nav>
