import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@core/auth';
import { Usuario } from '@core/auth/interfaces';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-menu-mobile',
  standalone: true,
  imports: [CommonModule, AvatarModule, ButtonModule],
  templateUrl: './menu-mobile.component.html',
  styleUrls: ['./menu-mobile.component.scss']
})
export class MenuMobileComponent implements OnInit {

  private router = inject(Router);
  private authService = inject(AuthService);

  // Dados do usuário
  usuario = signal<Usuario | null>(null);

  activeItem = signal<string>(this.router.url);

  menuItems = [
    {
      label: 'Operações',
      icon: 'pi pi-cog',
      items: [
        {
          label: 'Containers',
          icon: 'pi pi-box',
          routerLink: '/containers'
        },
        {
          label: 'Pedidos',
          icon: 'pi pi-file-edit',
          routerLink: '/pedidos',
          disabled: true
        }
      ]
    },
    {
      label: 'Clientes',
      icon: 'pi pi-users',
      routerLink: '/clientes',
      disabled: true
    }
  ];

  ngOnInit(): void {
    // Observa mudanças no usuário
    this.authService.usuario$.subscribe(usuario => {
      this.usuario.set(usuario);
    });
  }

  navigate(route: string): void {
    this.router.navigate([route]);
    this.activeItem.set(route);
  }

  isActive(route: string): boolean {
    return this.activeItem() === route;
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}