.menu-mobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.menu-section {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;

  .section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    font-size: 0.9rem;
    color: #4e6a93 !important;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
  }

  .menu-item {
    width: 100%;
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    color: #495057 !important;
    border-radius: 6px;
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: #f8f9fa;
      // transform: translateX(2px);
    }

    &.active {
      background-color: #e7f3ff;
      box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
      font-weight: 600;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    i {
      font-size: 1.1rem;
    }
  }
}

.user-section {
  margin-top: auto;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;

  .user-info {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;

    .user-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .user-name {
        font-weight: 600;
        color: #4e6a93 !important;
        font-size: 0.95rem;
      }

      .user-email {
        color: #4e6a93 !important;
        font-size: 0.8rem;
        opacity: 0.8;
      }

      .filial-info {
        margin-top: 0.5rem;
        padding: 0.5rem;
        background-color: #fff;
        border-radius: 6px;
        border: 1px solid #dee2e6;

        .filial-name {
          display: block;
          font-weight: 600;
          color: #28a745 !important;
          font-size: 0.75rem;
          margin-bottom: 0.25rem;
        }

        .filial-address {
          display: block;
          color: #4e6a93 !important;
          font-size: 0.7rem;
          opacity: 0.8;
        }
      }
    }
  }

  .user-loading {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4e6a93 !important;
    font-size: 0.9rem;
  }

  .user-actions {
    ::ng-deep .logout-btn {
      background-color: #dc3545 !important;
      border-color: #dc3545 !important;
      color: white !important;
      font-size: 0.8rem;
      height: 2.2rem;

      &:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
      }

      .p-button-label {
        font-weight: 500;
      }
    }
  }
}
