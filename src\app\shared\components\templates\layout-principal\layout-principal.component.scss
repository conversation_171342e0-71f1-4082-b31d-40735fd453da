.layout-principal {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f8f9fa;
  position: relative;
}

.menu-lateral-container {
  flex-shrink: 0;
  z-index: 100;
}

.conteudo-principal {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.header-controles {
  flex-shrink: 0;
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.area-conteudo {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.mapa-container {
  flex: 1;
  position: relative;
  background-color: #e9ecef;
  overflow: hidden;
}

.filtros-panel {
  flex-shrink: 0;
  width: 0;
  overflow: visible;
  transition: all 0.3s ease-in-out;
  transform: translateX(100%);
  opacity: 0;
  z-index: 200;
  position: relative;

  &.visible {
    width: 350px;
    transform: translateX(0);
    opacity: 1;
    overflow: visible;
  }
}

// Overlays
.loading-overlay,
.error-overlay,
.no-data-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);

  .loading-content {
    text-align: center;
    color: #6c757d;
  }
}

.error-overlay {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2rem;

  ::ng-deep {
    .p-message {
      max-width: 400px;
    }
  }
}

.no-data-overlay {
  background-color: rgba(255, 255, 255, 0.95);

  .no-data-content {
    max-width: 400px;
    padding: 2rem;

    i {
      display: block;
    }

    h5 {
      margin-bottom: 0.5rem;
    }

    p {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
  }
}

// Customizações PrimeNG
::ng-deep {
  .p-button {
    border-radius: 6px;
    font-weight: 500;

    &.p-button-outlined {
      background-color: transparent;
    }
  }

  .p-progressspinner-circle {
    stroke: #007bff;
  }

  .filtros-sidebar {
    width: 70% !important;

    .p-sidebar-content {
      padding: 0;
    }
  }

  .p-sidebar-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
  }

  // Ajustes específicos para o painel de filtros dentro do sidebar customizado
  app-sidebar {
    app-painel-filtros {
      .painel-filtros {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
        border: none;
        box-shadow: none;
        border-radius: 0;

        .p-card {
          height: calc(100vh - 120px);

          .p-card-content {
            max-height: calc(100vh - 160px);
            min-height: 300px;
          }
        }
      }
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .layout-principal {
    flex-direction: column;
  }

  .menu-lateral-container {
    display: none; // Menu lateral será substituído por um menu mobile
  }

  .conteudo-principal {
    padding-bottom: 80px; // Mais espaço para rodapé em mobile
  }

  .header-controles {
    .flex {
      flex-wrap: wrap;
      gap: 0.5rem;

      h4 {
        font-size: 1.1rem;
      }

      .text-sm {
        font-size: 0.75rem;
      }
    }
  }

  .no-data-overlay {
    .no-data-content {
      padding: 1rem;
      max-width: 300px;

      i {
        font-size: 3rem !important;
      }

      h5 {
        font-size: 1rem;
      }

      p {
        font-size: 0.875rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .header-controles {
    .flex {
      flex-direction: column;
      align-items: flex-start;

      &:last-child {
        align-items: flex-end;
        width: 100%;
        margin-top: 0.5rem;
      }
    }
  }
}

// Media queries para dispositivos com altura pequena
@media (max-height: 500px) {
  ::ng-deep {
    app-sidebar {
      app-painel-filtros {
        .painel-filtros {
          .p-card {
            height: calc(100vh - 80px);

            .p-card-content {
              max-height: calc(100vh - 120px);
              min-height: 200px;
              padding: 0.5rem;
            }
          }
        }
      }
    }
  }
}

@media (max-height: 600px) {
  ::ng-deep {
    app-sidebar {
      app-painel-filtros {
        .painel-filtros {
          .p-card {
            height: calc(100vh - 100px);

            .p-card-content {
              max-height: calc(100vh - 140px);
              min-height: 250px;
              padding: 0.75rem;
            }
          }
        }
      }
    }
  }
}

// Animações
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-content,
.no-data-content {
  animation: fadeIn 0.3s ease-in-out;
}

// Estados de hover para botões
.header-controles {
  .p-button {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.footer-mobile-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-around;
  padding: 0.75rem 1rem;
  z-index: 999;

  button {
    flex: 1;
    margin: 0 0.25rem;
    padding: 0.75rem;
    font-weight: bold;
    font-size: 0.9rem;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: background 0.2s ease-in-out;

    &:hover {
      background-color: #e2e6ea;
    }
  }

  .menu-button {
    color: var(--p-surface-500);
    border-color: var(--p-green-700);
  }

  .filtros-button {
    color: var(--p-surface-500);
    border-color: var(--p-green-700);
  }
}

.botoes-header-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-end;
  align-items: center;

  // Cada botão se adapta à largura
  ::ng-deep .p-button {
    flex: 1 1 auto;
    max-width: unset;
  }

  // Responsividade
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;

    ::ng-deep .p-button {
      width: 100%;
      flex: none;
    }
  }
}
