.rodape {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e9ecef;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rodape-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  padding: 0 2rem;
  max-width: 1200px;
  width: 100%;
}

.empresa-logo {
  display: flex;
  align-items: center;

  .logo-placeholder {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: 2px solid #007bff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007bff;
  }

  img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 4px;
  }
}

.empresa-info {
  display: flex;
  align-items: center;

  .empresa-nome {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
  }
}

.usuario-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .usuario-detalhes {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .usuario-nome {
      font-size: 0.875rem;
      font-weight: 600;
      color: #495057;
      line-height: 1.2;
    }

    .usuario-cargo {
      font-size: 0.75rem;
      color: #6c757d;
      line-height: 1.2;
    }
  }
}

.data-hora {
  display: flex;
  align-items: center;
  color: #6c757d;

  i {
    color: #007bff;
  }

  .data-hora-texto {
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Courier New', monospace;
  }
}

// Customizações PrimeNG
::ng-deep {
  .divider-custom {
    .p-divider-vertical {
      height: 30px;
      margin: 0;

      &:before {
        border-left: 1px solid #dee2e6;
      }
    }
  }

  .p-avatar {
    flex-shrink: 0;
  }
}

// Responsividade
@media (max-width: 768px) {
  .rodape-content {
    gap: 1rem;
    padding: 0 1rem;
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .empresa-info .empresa-nome {
    font-size: 0.875rem;
  }

  .usuario-info {
    gap: 0.5rem;

    .usuario-detalhes {
      .usuario-nome {
        font-size: 0.8rem;
      }

      .usuario-cargo {
        font-size: 0.7rem;
      }
    }
  }

  .data-hora .data-hora-texto {
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .rodape {
    height: auto;
    min-height: 60px;
    padding: 0.5rem 0;
  }

  .rodape-content {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
  }

  .divider-custom {
    display: none;
  }

  .empresa-info,
  .usuario-info,
  .data-hora {
    justify-content: center;
    text-align: center;
  }
}
