<div class="mapa-google-container h-full w-full">
  <google-map [center]="center()" [zoom]="zoom()" [options]="options()" class="w-full h-full">

    <!-- Marcadores -->
    @for (marker of markers(); track $index; let ix = $index) {
    <map-marker [position]="marker.position" [title]="marker.title" [options]="marker.options"
      (mapClick)="onMarkerClick(ix, marker)">


    </map-marker>
    }

    <map-info-window [position]="infoWindowPosition()" (closeClick)="closeInfoWindow()">
      <div [innerHTML]="infoWindowContent()"></div>
    </map-info-window>
  </google-map>
</div>