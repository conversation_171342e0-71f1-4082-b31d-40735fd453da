import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

/**
 * Guard de autenticação que protege rotas que requerem login
 * Redireciona para /login se o usuário não estiver autenticado
 */
export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Verifica se o usuário está logado
  if (authService.isLoggedIn()) {
    return true;
  }

  // Se não estiver logado, redireciona para login
  router.navigate(['/login']);
  return false;
};

/**
 * Guard que redireciona usuários já logados para a página principal
 * Usado na rota de login para evitar que usuários logados acessem a tela de login
 */
export const loginGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Se o usuário já estiver logado, redireciona para containers
  if (authService.isLoggedIn()) {
    router.navigate(['/containers']);
    return false;
  }

  // Se não estiver logado, permite acesso à tela de login
  return true;
};
