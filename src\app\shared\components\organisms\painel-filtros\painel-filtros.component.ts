import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  signal
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// PrimeNG Components
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';

import { TIPOS_EQUIPAMENTO } from '@core/models/equipamento.model';
import { MapaFiltros } from '@core/models/filtros.model';
import { BreakpointService } from '@core/services/breakpoint.service';
import { EquipamentoService } from '@core/services/equipamento.service';
import { MapaFiltrosService } from '@core/services/mapa-filtros.service';
import { ConfirmationService } from 'primeng/api';
import { SidebarComponent } from '../sidebar/sidebar.component';

@Component({
  selector: 'app-painel-filtros',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    MultiSelectModule,
    AutoCompleteModule,
    CalendarModule,
    InputTextModule,
    DividerModule,
    BadgeModule
  ],
  providers: [ConfirmationService],
  templateUrl: './painel-filtros.component.html',
  styleUrl: './painel-filtros.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PainelFiltrosComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private filtrosService = inject(MapaFiltrosService);
  private equipamentoService = inject(EquipamentoService);
  private breakpointService = inject(BreakpointService);
  public isMobile = this.breakpointService.isXSmall;
  public isWeb = computed(() => !this.isMobile());
  public closeSidebarEvent = output<boolean>();
  public filtrosTemporarios = this.filtrosService.filtrosTemporarios;
  private confirmationService = inject(ConfirmationService);
  public sidebarRef = input<SidebarComponent | null>();

  filtros = signal<MapaFiltros>(this.filtrosService.getFiltros());
  // Opções para os selects
  tiposEquipamento = TIPOS_EQUIPAMENTO.map(tipo => ({
    label: tipo.descricao,
    value: tipo.codigo,
    icon: tipo.icone
  }));

  // Listas para autocomplete
  servicosSuggestions = signal<string[]>([]);
  clientesSuggestions = signal<string[]>([]);
  bairrosSuggestions = signal<string[]>([]);
  solicitantesSuggestions = signal<string[]>([]);

  constructor() {
    effect(() => {
      if (this.sidebarRef()) {
        if (this.sidebarRef()?.closeAttempt()) {
          this.validatePendingFilters();
          console.log('tentativa de fechar');
        }
      }
    })
  }

  ngOnInit(): void {
    this.filtrosService.filtros$
      .pipe(takeUntil(this.destroy$))
      .subscribe(filtros => {
        this.filtros.set(filtros);
        this.filtrosService.updateFiltrosTemporarios(filtros);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }


  private updateFiltrosCondicional(filtrosParaAtualizar: Partial<MapaFiltros>): void {
    const filtrosAtuais = this.filtrosService.getFiltrosTemporarios();
    const novosFiltrosTemporarios = { ...filtrosAtuais, ...filtrosParaAtualizar };
    this.filtrosService.updateFiltrosTemporarios(novosFiltrosTemporarios)

    if (this.isWeb()) {
      this.filtrosService.updateFiltros(novosFiltrosTemporarios);
    }
  }

  /**
   * Atualiza os tipos de equipamento selecionados
   */
  onTiposEquipamentoChange(tipos: string[]): void {
    const tiposValidos = tipos || [];
    this.updateFiltrosCondicional({ tiposEquipamento: tiposValidos });
  }

  /**
   * Busca sugestões de serviços
   */
  searchServicos(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllServices(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(servicos => {
        this.servicosSuggestions.set(servicos);
      });
  }

  /**
   * Atualiza os serviços selecionados
   */
  onServicosChange(servicos: string[]): void {
    this.updateFiltrosCondicional({ servicos });
  }

  /**
   * Busca sugestões de clientes
   */
  searchClientes(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllClients(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(clientes => {
        this.clientesSuggestions.set(clientes);
      });
  }

  /**
   * Atualiza os clientes selecionados
   */
  onClientesChange(clientes: string[]): void {
    this.updateFiltrosCondicional({ clientes });
  }

  /**
   * Busca sugestões de bairros
   */
  searchBairros(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllPlaces(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(bairros => {
        this.bairrosSuggestions.set(bairros);
      });
  }

  /**
   * Atualiza os bairros selecionados
   */
  onBairrosChange(bairros: string[]): void {
    this.updateFiltrosCondicional({ bairros });
  }

  onIdsChange(ids: string[]): void {
    const cleaned = (ids || []).map(id => (id ?? '').toString().replace(/[^0-9]/g, '')).filter(id => id.length > 0);
    this.updateFiltrosCondicional({ ids: cleaned });
  }

  /**
   * Busca sugestões de solicitantes
   */
  searchSolicitantes(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllRequesters(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(solicitantes => {
        this.solicitantesSuggestions.set(solicitantes);
      });
  }

  /**
   * Atualiza os solicitantes selecionados
   */
  onSolicitantesChange(solicitantes: string[]): void {
    this.updateFiltrosCondicional({ solicitantes });
  }

  /**
   * Atualiza a data de início
   */
  onDataInicioChange(data: Date): void {
    this.updateFiltrosCondicional({ dataInicio: data });
  }

  /**
   * Atualiza a data de fim
   */
  onDataFimChange(data: Date): void {
    this.updateFiltrosCondicional({ dataFim: data });
  }

  /**
   * Limpa todos os filtros (tanto locais quanto aplicados)
   */
  clearFilters(): void {
    this.filtrosService.resetFiltros();
    this.filtrosService.resetFiltrosTemporarios();
    this.emitCloseSidebarEvent();
  }

  /**
   * Aplica os filtros temporários
   */
  applyFilters(): void {
    const filtrosParaAplicar = this.filtrosService.getFiltrosTemporarios();
    this.filtrosService.updateFiltros(filtrosParaAplicar);

    this.emitCloseSidebarEvent();
  }

  emitCloseSidebarEvent() {
    if (this.isMobile()) {
      this.closeSidebarEvent.emit(true);
    }
  }

  allFiltersEmpty() {
    const filters = this.filtrosService.getFiltrosTemporarios();
    const emptyFilters = Object.entries(filters).every(([_, value]) => Array.isArray(value) && value.length === 0);

    return emptyFilters;
  }

  validatePendingFilters() {
    if (this.allFiltersEmpty()) {
      this.emitCloseSidebarEvent();
    } else {
      this.messageNotAppliedFilters();
    }
  }

  private messageNotAppliedFilters() {
    this.confirmationService.confirm({
      message: 'Você tem filtros não aplicados.',
      header: 'Aviso',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Descartar',
      acceptButtonStyleClass: 'p-button-danger',
      rejectLabel: 'Aplicar',
      accept: () => this.clearFilters(),
      reject: () => this.applyFilters()
    })
  }
}
