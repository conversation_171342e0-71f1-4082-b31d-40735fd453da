import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { interval, Subject, takeUntil } from 'rxjs';

// PrimeNG Components
import { AvatarModule } from 'primeng/avatar';
import { DividerModule } from 'primeng/divider';

@Component({
  selector: 'app-rodape',
  standalone: true,
  imports: [
    CommonModule,
    AvatarModule,
    DividerModule
  ],
  templateUrl: './rodape.component.html',
  styleUrl: './rodape.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RodapeComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Dados da empresa (mockados para futura integração)
  empresaInfo = signal({
    logo: 'assets/images/logo-empresa.png', // Placeholder para logo
    nome: 'Portal Caçambas Ltda.',
    usuario: '<PERSON>',
    cargo: 'Administrador'
  });

  // Data e hora atual
  dataHoraAtual = signal<string>('');

  ngOnInit(): void {
    this.updateDateTime();
    
    // Atualiza a data/hora a cada segundo
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateDateTime();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Atualiza a data e hora atual
   */
  private updateDateTime(): void {
    const agora = new Date();
    const dataFormatada = agora.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    const horaFormatada = agora.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    this.dataHoraAtual.set(`${dataFormatada} - ${horaFormatada}`);
  }
}
