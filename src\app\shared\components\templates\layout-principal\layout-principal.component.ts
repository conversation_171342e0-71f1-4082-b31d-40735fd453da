import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
  ViewChild
} from '@angular/core';
import { debounceTime, Subject, switchMap, takeUntil } from 'rxjs';

// PrimeNG Components
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SidebarModule } from 'primeng/sidebar';
import { TooltipModule } from 'primeng/tooltip';

// Custom Components
// import { InfoUsuarioFilialComponent } from '@shared/components/molecules/info-usuario-filial/info-usuario-filial.component';
import { MapaGoogleComponent } from '@shared/components/organisms/mapa-google/mapa-google.component';
import { MenuLateralComponent } from '@shared/components/organisms/menu-lateral/menu-lateral.component';
import { PainelFiltrosComponent } from '@shared/components/organisms/painel-filtros/painel-filtros.component';

// Services and Models
import { EquipamentoResponse } from '@core/models/equipamento.model';
import { EquipamentoService } from '@core/services/equipamento.service';
import { MapaFiltrosService } from '@core/services/mapa-filtros.service';
import { ListagemEquipamentosSemGeolocalizacaoComponent } from '@shared/components/organisms/listagem-equipamentos-sem-geolocalizacao/listagem-equipamentos-sem-geolocalizacao.component';
import { MenuMobileComponent } from '@shared/components/organisms/menu-mobile/menu-mobile.component';
import { SidebarComponent } from '@shared/components/organisms/sidebar/sidebar.component';
import { ConfirmationService } from 'primeng/api';
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-layout-principal',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    SidebarModule,
    ProgressSpinnerModule,
    MessageModule,
    TooltipModule,
    MenuLateralComponent,
    MapaGoogleComponent,
    PainelFiltrosComponent,
    SidebarComponent,
    MenuMobileComponent,
    DynamicDialogModule,
    ConfirmDialogModule
  ],
  providers: [ConfirmationService],
  templateUrl: './layout-principal.component.html',
  styleUrl: './layout-principal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutPrincipalComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private filtrosService = inject(MapaFiltrosService);
  private equipamentoService = inject(EquipamentoService);
  private cdr = inject(ChangeDetectorRef)
  public menuMobileVisible = signal(false);
  private dialogService = inject(DialogService);
  private confirmationService = inject(ConfirmationService);

  @ViewChild('mapaGoogle') mapaComponent!: MapaGoogleComponent;

  // Signals para reatividade
  equipamentos = signal<EquipamentoResponse[]>([]);
  loading = signal<boolean>(false);
  error = signal<string | null>(null);
  filtrosVisible = signal<boolean>(true);
  isMobile = signal<boolean>(false);

  ngOnInit(): void {
    this.checkScreenSize();
    this.setupFiltrosSubscription();
    this.loadInitialData();

    // Listener para mudanças de tamanho da tela
    window.addEventListener('resize', () => this.checkScreenSize());
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', () => this.checkScreenSize());
  }


  /**
   * Configura a subscription para mudanças nos filtros
   */
  private setupFiltrosSubscription(): void {
    this.filtrosService.filtros$
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Debounce para evitar muitas requisições
        switchMap(filtros => {
          this.loading.set(true);
          this.error.set(null);

          const equipamentoFiltro = this.filtrosService.toEquipamentoFiltro(filtros);
          return this.equipamentoService.findAll(equipamentoFiltro);
        })
      )
      .subscribe({
        next: (equipamentos) => {
          this.equipamentos.set(equipamentos);
          this.loading.set(false);

          // Atualiza o mapa com os novos equipamentos
          if (this.mapaComponent) {
            this.mapaComponent.updateMarkers();
          }
        },
        error: (error) => {
          console.error('Erro ao carregar equipamentos:', error);
          this.error.set('Erro ao carregar equipamentos. Tente novamente.');
          this.loading.set(false);
        }
      });
  }

  /**
   * Carrega dados iniciais
   */
  private loadInitialData(): void {
    this.loading.set(true);

    const filtrosIniciais = this.filtrosService.toEquipamentoFiltro(this.filtrosService.getFiltros());

    this.equipamentoService.findAll(filtrosIniciais)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (equipamentos) => {
          this.equipamentos.set(equipamentos);
          this.loading.set(false);
        },
        error: (error) => {
          console.error('Erro ao carregar dados iniciais:', error);
          this.error.set('Erro ao carregar dados iniciais. Tente novamente.');
          this.loading.set(false);
        }
      });
  }

  /**
   * Verifica o tamanho da tela para responsividade
   */
  private checkScreenSize(): void {
    this.isMobile.set(window.innerWidth < 768);

    // Em mobile, fecha o painel de filtros automaticamente
    if (this.isMobile() && this.filtrosVisible()) {
      this.filtrosVisible.set(false);
    }
  }

  /**
   * Toggle do painel de filtros
   */
  toggleFiltros(): void {
    this.filtrosVisible.set(!this.filtrosVisible());
    if (!this.filtrosVisible()) {

    }
    this.cdr.detectChanges()
  }

  toggleMenuMobile(): void {
    this.menuMobileVisible.set(!this.menuMobileVisible());
    this.cdr.detectChanges()
  }

  /**
   * Fecha o painel de filtros
   */
  closeMobileFiltersPanel(): void {
    this.filtrosVisible.set(false);
  }

  /**
   * Recarrega os dados
   */
  reloadData(): void {
    this.loadInitialData();
  }

  /**
   * Centraliza o mapa em Brasília
   */
  centerMapBrasilia(): void {
    if (this.mapaComponent) {
      this.mapaComponent.centerMap(-15.7942, -47.8822, 10);
    }
  }

  /**
   * Ajusta o zoom para mostrar todos os equipamentos
   */
  fitAllEquipments(): void {
    if (this.mapaComponent && this.equipamentos().length > 0) {
      this.mapaComponent.fitBounds();
    }
  }

  /**
   * Limpa todos os filtros
   */
  clearFilters(): void {
    this.filtrosService.resetFiltros();
  }

  openModalSemGeolocalizacao() {
    this.dialogService.open(ListagemEquipamentosSemGeolocalizacaoComponent, {
      header: 'Equipamentos sem Geolocalização',
      modal: true,
      width: '70%',
      height: '70%',
      maximizable: true,
      closable: true
    });
  }
}
