<div class="menu-lateral" [class.collapsed]="collapsed()">
  <!-- Header do Menu -->
  <div class="menu-header" (click)="toggleCollapsed()">
    <div class="logo-container" [class.collapsed]="collapsed()">
      @if (!collapsed()) {
        <div class="logo-content">
          <i class="pi pi-map text-green-700 text-2xl"></i>
          <span class="logo-text text-green-700">Portal Caçambas</span>
        </div>
      } @else {
        <i class="pi pi-map text-green-700 text-2xl"></i>
      }
    </div>
    <i [class]="collapsed() ? 'pi pi-angle-right icon-arrow' : 'pi pi-angle-left icon-arrow'"></i>
  </div>

  <!-- Navegação Principal -->
  <nav class="menu-nav">

    <!-- Seção Operações -->
    <div class="menu-section">
      @if (!collapsed()) {
        <div class="section-header">
          <i class="pi pi-cog section-icon"></i>
          <span class="section-title">Operações</span>
        </div>
      } @else {
        <div class="section-divider"></div>
      }

      <!-- Containers -->
      <div class="menu-item-container">
        <p-button
          label="Containers"
          [text]="true"
          [class]="'menu-item ' + (isActive('/containers') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          routerLink="/containers"
          pRipple>
          <i class="pi pi-box" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Containers</span>
          }
        </p-button>
      </div>

      <!-- Pedidos -->
      <div class="menu-item-container">
        <p-button
          label="Pedidos"
          [text]="true"
          [class]="'menu-item ' + (isActive('/pedidos') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          [disabled]="true"
          pRipple>
          <i class="pi pi-file-edit" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Pedidos</span>
          }
        </p-button>
      </div>
    </div>

    <!-- Seção Clientes -->
    <div class="menu-section">
      @if (!collapsed()) {
        <div class="section-header">
          <i class="pi pi-users section-icon"></i>
          <span class="section-title">Clientes</span>
        </div>
      } @else {
        <div class="section-divider"></div>
      }

      <!-- Clientes -->
      <div class="menu-item-container">
        <p-button
          label="Clientes"
          [text]="true"
          [class]="'menu-item ' + (isActive('/clientes') ? 'active' : '')"
          [style]="{ 'justify-content': collapsed() ? 'center' : 'flex-start' }"
          [disabled]="true"
          pRipple>
          <i class="pi pi-users" pButtonIcon></i>
          @if (!collapsed()) {
            <span class="menu-label">Clientes</span>
          }
        </p-button>
      </div>
    </div>
  </nav>

  <!-- Footer do Menu -->
  <div class="menu-footer">
    @if (!collapsed()) {
      <div class="user-info" [class.expanded]="userInfoExpanded()" (click)="toggleUserInfo()">
        <div class="user-header">
          <p-avatar
            image="assets/images/ecoavatar.png"
            size="large"
            shape="circle"
            [style]="{ 'background-color': 'var(--green-700)', 'color': 'white' }">
          </p-avatar>
          @if (usuario()) {
            <div class="user-basic">
              <span class="user-name">{{ usuario()?.nome }}</span>
              <!-- <span class="user-role">{{ usuario()?.cargo || 'Usuário' }}</span> -->
            </div>
          } @else {
            <div class="user-basic">
              <span class="user-name">Carregando...</span>
              <span class="user-role">-</span>
            </div>
          }
          <i class="pi" [class.pi-chevron-up]="userInfoExpanded()" [class.pi-chevron-down]="!userInfoExpanded()"></i>
        </div>

        @if (userInfoExpanded() && usuario()) {
          <div class="user-expanded">
            <!-- <div class="user-email">
              <i class="pi pi-envelope"></i>
              <span>{{ usuario()?.email }}</span>
            </div> -->
            @if (usuario()?.filialAtual) {
              <div class="filial-info">
                <div class="filial-header">
                  <i class="pi pi-building"></i>
                  <span class="filial-label">Filial Atual</span>
                </div>
                <div class="filial-details">
                  <span class="filial-name">{{ usuario()?.filialAtual?.codigoFormatado }} - {{ usuario()?.filialAtual?.nome }}</span>
                  <span class="filial-address">{{ usuario()?.filialAtual?.endereco }}</span>
                  <span class="filial-city">{{ usuario()?.filialAtual?.cidade }}/{{ usuario()?.filialAtual?.uf }}</span>
                </div>
              </div>
            }
            <div class="user-actions">
              <p-button
                icon="pi pi-sign-out"
                label="Sair"
                severity="secondary"
                size="small"
                styleClass="w-full"
                (onClick)="logout()">
              </p-button>
            </div>
          </div>
        }
      </div>
    } @else {
      <p-avatar
        image="assets/images/ecoavatar.png"
        size="normal"
        shape="circle"
        [style]="{ 'background-color': 'var(--green-700)', 'color': 'white' }"
        [pTooltip]="usuario()?.nome || 'Usuário'"
        tooltipPosition="right">
      </p-avatar>
    }
  </div>
</div>
