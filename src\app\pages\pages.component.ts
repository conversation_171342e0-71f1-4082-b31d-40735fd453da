import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { LayoutComponent } from '@core/layout/layout.component';

@Component({
  selector: 'app-pages',
  standalone: true,
  imports: [RouterOutlet, LayoutComponent],
  template: `
    <app-layout>
      <router-outlet />
    </app-layout>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PagesComponent {}
