import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable, catchError, map, tap } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Filial, FilialResponse, LoginRequest, LoginResponse, Usuario } from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly baseUrl = environment.apiUrl;

  // Signals para reatividade
  private usuarioSubject = new BehaviorSubject<Usuario | null>(null);
  public usuario$ = this.usuarioSubject.asObservable();
  public usuario = signal<Usuario | null>(null);

  private filialAtualSubject = new BehaviorSubject<Filial | null>(null);
  public filialAtual$ = this.filialAtualSubject.asObservable();
  public filialAtual = signal<Filial | null>(null);

  private filiaisDisponiveisSubject = new BehaviorSubject<Filial[]>([]);
  public filiaisDisponiveis$ = this.filiaisDisponiveisSubject.asObservable();
  public filiaisDisponiveis = signal<Filial[]>([]);

  // Estado de loading
  public loading = signal<boolean>(false);

  // Dados mock para desenvolvimento
  private mockFiliais: Filial[] = [
    {
      id: '1',
      codigo: '0001',
      codigoFormatado: '0001',
      nome: 'ECO VISAO',
      endereco: 'SAAN QUADRA 03 LOTE 180, ZONA INDUSTRIAL',
      bairro: 'ZONA INDUSTRIAL',
      cidade: 'BRASILIA',
      uf: 'DF',
      ativa: true
    }
  ];

  constructor(private http: HttpClient) {
    // Verifica se há dados de usuário salvos no localStorage
    this.checkStoredAuth();
  }

  /**
   * Realiza login do usuário
   */
  login(credentials: LoginRequest): Observable<LoginResponse> {
    this.loading.set(true);

    const url = `http://**************:8080/api/auth/login?username=${credentials.username}&password=${credentials.password}`;

    return this.http.post<LoginResponse>(url, credentials).pipe(
      tap(response => {
        const usuario: Usuario = {
          id: response.codigo.toString(),
          nome: this.formatarNome(response.nome),
          email: response.email,
          cargo: response.situacao,
          ativo: true,
          filialAtual: this.mockFiliais[0],
          filiaisDisponiveis: this.mockFiliais
        };

        this.setUsuario(usuario);
        this.setFilialAtual(this.mockFiliais[0]);
        this.loading.set(false);

        localStorage.setItem('userInfo', JSON.stringify({
          codigo: response.codigo,
          nome: response.nome,
          email: response.email
        }));
      }),
      catchError(error => {
        this.loading.set(false);
        console.error('Erro no login:', error);
        throw error;
      })
    );
  }

  /**
   * Busca informações do usuário logado
   */
  getUsuarioLogado(): Observable<Usuario> {
    return this.http.get<Usuario>(`${this.baseUrl}/auth/me`)
      .pipe(
        tap(usuario => this.setUsuario(usuario))
      );
  }

  /**
   * Realiza logout do usuário
   */
  logout(): void {
    // Limpa dados do localStorage
    localStorage.removeItem('userInfo');
    localStorage.removeItem('filialAtual');

    // Reseta os signals
    this.usuario.set(null);
    this.usuarioSubject.next(null);
    this.filialAtual.set(null);
    this.filialAtualSubject.next(null);
    this.filiaisDisponiveis.set([]);
    this.filiaisDisponiveisSubject.next([]);
  }

  /**
   * Busca filiais disponíveis para o usuário
   */
  getFiliais(): Observable<Filial[]> {
    return this.http.get<FilialResponse[]>(`${this.baseUrl}/filiais`)
      .pipe(
        map(filiais => filiais.map(this.mapFilialResponse)),
        tap(filiais => {
          this.filiaisDisponiveis.set(filiais);
          this.filiaisDisponiveisSubject.next(filiais);

          // Define a primeira filial como atual se não houver uma selecionada
          if (filiais.length > 0 && !this.filialAtual()) {
            this.setFilialAtual(filiais[0]);
          }
        })
      );
  }

  /**
   * Define a filial atual
   */
  setFilialAtual(filial: Filial): void {
    const atual = this.filialAtual();
    if (atual && atual.id === filial.id) {
      return; // Evita recursão desnecessária
    }

    this.filialAtual.set(filial);
    this.filialAtualSubject.next(filial);

    // Atualiza o usuário com a nova filial atual
    const usuarioAtual = this.usuario();
    if (usuarioAtual) {
      // Só atualiza se for diferente
      if (!usuarioAtual.filialAtual || usuarioAtual.filialAtual.id !== filial.id) {
        const usuarioAtualizado = { ...usuarioAtual, filialAtual: filial };
        this.setUsuario(usuarioAtualizado);
      }
    }


    // Salva no localStorage para persistir a seleção
    localStorage.setItem('filialAtual', JSON.stringify(filial));
  }

  /**
   * Define o usuário atual
   */
  private setUsuario(usuario: Usuario): void {
    this.usuario.set(usuario);
    this.usuarioSubject.next(usuario);

    if (usuario.filialAtual) {
      this.setFilialAtual(usuario.filialAtual);
    }

    if (usuario.filiaisDisponiveis?.length > 0) {
      this.filiaisDisponiveis.set(usuario.filiaisDisponiveis);
      this.filiaisDisponiveisSubject.next(usuario.filiaisDisponiveis);
    }
  }

  /**
   * Mapeia resposta da API para interface Filial
   */
  private mapFilialResponse(filialResponse: FilialResponse): Filial {
    return {
      id: filialResponse.id,
      codigo: filialResponse.codigo,
      codigoFormatado: filialResponse.codigoFormatado,
      nome: filialResponse.nome,
      endereco: filialResponse.endereco,
      bairro: filialResponse.bairro,
      cidade: filialResponse.cidade,
      uf: filialResponse.uf,
      cep: filialResponse.cep,
      ativa: filialResponse.ativa
    };
  }

  /**
   * Inicializa dados mockados para desenvolvimento
   */
  private initializeMockData(): void {
    const filialMock: Filial = {
      id: '1',
      codigo: '0001',
      codigoFormatado: '0001',
      nome: 'ECO VISAO',
      endereco: 'SAAN QUADRA 03 LOTE 180, ZONA INDUSTRIAL',
      bairro: 'ZONA INDUSTRIAL',
      cidade: 'BRASILIA',
      uf: 'DF',
      ativa: true
    };

    const usuarioMock: Usuario = {
      id: '1',
      nome: 'Sistema Portal',
      email: '<EMAIL>',
      cargo: 'Administrador',
      ativo: true,
      filialAtual: filialMock,
      filiaisDisponiveis: [filialMock]
    };

    // Verifica se há dados salvos no localStorage
    const filialSalva = localStorage.getItem('filialAtual');
    if (filialSalva) {
      try {
        const filial = JSON.parse(filialSalva);
        usuarioMock.filialAtual = filial;
      } catch (error) {
        console.warn('Erro ao carregar filial salva:', error);
      }
    }

    this.setUsuario(usuarioMock);
  }

  private formatarNome(nomeCompleto: string | undefined | null): string {
    if (!nomeCompleto) return '';
    const partes = nomeCompleto.trim().split(' ');
    if (partes.length === 1) return partes[0];
    return `${partes[0]} ${partes[partes.length - 1]}`;
  }

  /**
   * Verifica se o usuário está logado
   */
  isLoggedIn(): boolean {
    const usuario = this.usuario();
    const userInfo = localStorage.getItem('userInfo');

    // Considera logado se há um usuário no signal OU dados salvos no localStorage
    return usuario !== null || userInfo !== null;
  }

  /**
   * Verifica se há dados de autenticação salvos no localStorage
   * e inicializa o usuário se encontrar
   */
  private checkStoredAuth(): void {
    const userInfo = localStorage.getItem('userInfo');

    if (userInfo) {
      try {
        const userData = JSON.parse(userInfo);

        // Reconstrói o usuário a partir dos dados salvos
        const usuario: Usuario = {
          id: userData.codigo?.toString() || '1',
          nome: this.formatarNome(userData.nome) || 'Usuário',
          email: userData.email || '',
          cargo: 'Usuário',
          ativo: true,
          filialAtual: this.mockFiliais[0],
          filiaisDisponiveis: this.mockFiliais
        };

        this.setUsuario(usuario);
      } catch (error) {
        console.warn('Erro ao carregar dados de autenticação salvos:', error);
        // Se houver erro, limpa os dados corrompidos
        localStorage.removeItem('userInfo');
      }
    }
    // Não inicializa dados mock automaticamente - usuário deve fazer login
  }
}
