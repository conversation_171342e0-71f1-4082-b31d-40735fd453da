import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '@core/auth';
import { LoginRequest } from '@core/auth/interfaces';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    InputTextModule,
    PasswordModule,
    ButtonModule,
    ProgressSpinnerModule,
    ToastModule
  ],
  providers: [MessageService],
  template: `
    <div class="login-container">
      <div class="login-card">
        <p-card>
          <ng-template pTemplate="header">
            <div class="login-header">
              <img src="assets/images/ecoavatar.png" alt="EcoVisão" class="logo">
              <h2>Portal Caçambas</h2>
              <p>Faça login para continuar</p>
            </div>
          </ng-template>
          
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="form-field">
              <label for="username">Usuário</label>
              <input 
                pInputText 
                id="username" 
                formControlName="username"
                placeholder="Digite seu usuário"
                [class.ng-invalid]="loginForm.get('username')?.invalid && loginForm.get('username')?.touched">
              <small 
                class="p-error" 
                *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched">
                Usuário é obrigatório
              </small>
            </div>

            <div class="form-field">
              <label for="password">Senha</label>
              <p-password 
                formControlName="password"
                placeholder="Digite sua senha"
                [feedback]="false"
                [toggleMask]="true"
                styleClass="w-full"
                inputStyleClass="w-full">
              </p-password>
              <small 
                class="p-error" 
                *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
                Senha é obrigatória
              </small>
            </div>

            <div class="form-actions">
              <p-button 
                type="submit"
                label="Entrar"
                icon="pi pi-sign-in"
                styleClass="w-full"
                [loading]="isLoading()"
                [disabled]="loginForm.invalid || isLoading()">
              </p-button>
            </div>
          </form>
        </p-card>
      </div>
    </div>
    
    <p-toast></p-toast>
  `,
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private messageService = inject(MessageService);

  isLoading = signal<boolean>(false);

  loginForm: FormGroup = this.fb.group({
    username: ['', [Validators.required]],
    password: ['', [Validators.required]]
  });

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading.set(true);

      const credentials: LoginRequest = {
        username: this.loginForm.value.username,
        password: this.loginForm.value.password
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading.set(false);
          this.messageService.add({
            severity: 'success',
            summary: 'Login realizado',
            detail: `Bem-vindo, ${response.nome}!`
          });

          // Redireciona para a página principal
          setTimeout(() => {
            this.router.navigate(['/containers']);
          }, 1000);
        },
        error: (error) => {
          this.isLoading.set(false);
          this.messageService.add({
            severity: 'error',
            summary: 'Erro no login',
            detail: error.error.message
          });
          console.error('Erro no login:', error);
        }
      });
    } else {
      // Marca todos os campos como touched para mostrar erros
      Object.keys(this.loginForm.controls).forEach(key => {
        this.loginForm.get(key)?.markAsTouched();
      });
    }
  }
}
