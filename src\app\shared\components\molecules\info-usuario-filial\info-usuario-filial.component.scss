.info-container {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.usuario-info, .filial-info, .endereco-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.label {
  font-weight: 600;
  min-width: 60px;
}

.value {
  color: #495057;
}

.cargo {
  color: #6c757d;
  font-style: italic;
}

.loading-info, .error-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
}

.error-info {
  color: #dc3545;
}

// Responsividade
@media (max-width: 768px) {
  .info-content {
    font-size: 0.7rem;
  }

  .usuario-info, .filial-info, .endereco-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .label {
    min-width: auto;
  }
}
