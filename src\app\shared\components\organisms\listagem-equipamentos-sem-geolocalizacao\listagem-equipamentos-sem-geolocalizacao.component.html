<p-card>
  <div class="flex justify-content-between align-items-center">
    <span class="text-danger font-bold">
      Total de equipamentos sem posicionamento: {{ equipamentos.length }}
    </span>
  </div>
</p-card>
@if (equipamentos.length > 0 && !loading) {
  <br>
<p-table [value]="equipamentos" showGridlines stripedRows  [paginator]="true" [rows]="10" [rowsPerPageOptions]="[10, 20, 50]"
  [responsiveLayout]="'scroll'" sortMode="multiple">

  <ng-template pTemplate="header">
    <tr>
      <th pSortableColumn="id">ID Equipamento <p-sortIcon field="id" /></th>
      <th>Tipo</th>
      <th pSortableColumn="clienteServico">Cliente Serviço <p-sortIcon field="clienteServico" /></th>
      <th pSortableColumn="clienteFaturamento">Cliente Faturamento <p-sortIcon field="clienteFaturamento" /></th>
      <th pSortableColumn="bairro">Bairro <p-sortIcon field="bairro" /></th>
      <th pSortableColumn="dataUltimaMov">Data Última Mov. <p-sortIcon field="dataUltimaMov" /></th>
      <th pSortableColumn="tempoDias">Tempo (Dias) <p-sortIcon field="tempoDias" /></th>
    </tr>
  </ng-template>

  <ng-template pTemplate="body" let-equipamento>
    <tr>
      <td>{{ equipamento.id }}</td>
      <td>
        <p-tag value="{{ equipamento.tipoEquipamento }}" severity="info" />
      </td>
      <td>{{ equipamento.nome }}</td>
      <td>{{ equipamento.nredFat }}</td>
      <td>{{ equipamento.bairro }}</td>
      <td>{{ equipamento.dataUltMovimento}}</td>
      <td>{{ equipamento.tempoDias }}</td>
    </tr>
  </ng-template>
</p-table>
} @else if(!loading) {
  <div class="flex justify-content-center align-items-center gap-2" style="height: 100%;">
    <i class="pi pi-exclamation-triangle text-2xl"></i>
    <span class="text-2xl font-bold">Nenhum equipamento sem geolocalização foi encontrado.</span>
  </div>
}