.mapa-google-container {
  position: relative;

  google-map {
    display: block;
    width: 100%;
    height: 100%;
  }
}

// Estilos para InfoWindow
::ng-deep {
  button.gm-style-iw-d {
    background-color: #dc3545 !important;
  }

  .gm-style-iw {
    // Remove padding padrão do Google Maps
    padding: 0 !important;
  }

  .gm-style-iw-c {
    // Remove padding padrão do container
    padding: 0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .gm-style-iw-d {
    // Remove overflow hidden para permitir scroll se necessário
    overflow: visible !important;
  }

  .info-window {
    min-width: 280px;
    max-width: 320px;
    font-family: "Roboto", sans-serif;
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-sizing: border-box;

    h6 {
      margin: 0 0 12px 0;
      color: #007bff;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 8px;
    }

    p {
      margin: 6px 0;
      font-size: 13px;
      line-height: 1.5;
      color: #6c757d;

      strong {
        color: #495057;
        font-weight: 600;
      }
    }

    // Estilos para o cabeçalho com ID e ícone
    .header-info {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      align-items: center;
      gap: 8px;

      span {
        font-size: 11px;
        font-weight: bold;
        color: #6c757d;
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 4px;
      }

      img {
        width: 28px;
        height: 28px;
        border-radius: 4px;
      }
    }
  }

  // Customizar o botão de fechar
  .gm-style-iw-t::after {
    background-color: #dc3545 !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    top: 8px !important;
    right: 8px !important;
  }
}
