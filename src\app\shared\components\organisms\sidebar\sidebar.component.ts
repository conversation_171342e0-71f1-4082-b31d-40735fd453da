import { Component, HostListener, input, output, signal } from '@angular/core';

@Component({
  selector: 'app-sidebar',
  imports: [],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  title = input.required<string>();
  visible = input.required<boolean>();
  close = output()
  readonly closeAttempt = signal(false);

  // Fecha ao pressionar ESC
  @HostListener('document:keydown.escape')
  onEsc() {
    this.close.emit();
    this.closeAttempt.set(true);
  }

  closeSidebar() {
    this.close.emit();
    this.closeAttempt.set(true);
  }
}
