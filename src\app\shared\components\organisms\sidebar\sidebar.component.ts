import { Component, HostListener, input, output } from '@angular/core';

@Component({
  selector: 'app-sidebar',
  imports: [],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  title = input.required<string>();
  visible = input.required<boolean>();
  close = output()

  // Fecha ao pressionar ESC
  @HostListener('document:keydown.escape')
  onEsc() {
    this.close.emit();
  }

  closeSidebar() {
    this.close.emit();
  }
}
