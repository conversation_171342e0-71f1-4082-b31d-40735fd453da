import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { AuthService } from '@core/auth';
import { Filial, Usuario } from '@core/auth/interfaces';

@Component({
  selector: 'app-info-usuario-filial',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="info-container">
      @if (usuario() && filialAtual()) {
        <div class="info-content">
          <div class="usuario-info">
            <span class="label">Usuário:</span>
            <span class="value">{{ usuario()?.nome }}</span>
            @if (usuario()?.cargo) {
              <span class="cargo">({{ usuario()?.cargo }})</span>
            }
          </div>

          <div class="filial-info">
            <span class="label">Filial:</span>
            <span class="value">{{ filialAtual()?.codigoFormatado }} - {{ filialAtual()?.nome }}</span>
          </div>

          <div class="endereco-info">
            <span class="label">Endereço:</span>
            <span class="value">{{ filialAtual()?.endereco }}, {{ filialAtual()?.bairro }} - {{ filialAtual()?.cidade }}/{{ filialAtual()?.uf }}</span>
          </div>
        </div>
      } @else if (loading()) {
        <div class="loading-info">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Carregando informações...</span>
        </div>
      } @else if (error()) {
        <div class="error-info">
          <i class="pi pi-exclamation-triangle"></i>
          <span>Erro ao carregar informações</span>
        </div>
      }
    </div>
  `,
  styleUrls: ['./info-usuario-filial.component.scss']
})
export class InfoUsuarioFilialComponent implements OnInit {
  private authService = inject(AuthService);

  // Signals
  usuario = signal<Usuario | null>(null);
  filialAtual = signal<Filial | null>(null);
  loading = signal<boolean>(false);
  error = signal<boolean>(false);

  ngOnInit(): void {
    this.loadUserInfo();
    this.setupSubscriptions();
  }

  private loadUserInfo(): void {
    this.loading.set(true);
    this.error.set(false);

    // Carrega filiais disponíveis e define a primeira como padrão
    this.authService.getFiliais().subscribe({
      next: (filiais) => {
        if (filiais.length > 0) {
          // Sempre usa o índice 0 como padrão
          this.authService.setFilialAtual(filiais[0]);
        }
        this.loading.set(false);
      },
      error: (err) => {
        console.error('Erro ao carregar filiais:', err);
        this.error.set(true);
        this.loading.set(false);
      }
    });
  }

  private setupSubscriptions(): void {
    // Observa mudanças no usuário
    this.authService.usuario$.subscribe(usuario => {
      this.usuario.set(usuario);
    });

    // Observa mudanças na filial atual
    this.authService.filialAtual$.subscribe(filial => {
      this.filialAtual.set(filial);
    });
  }
}
