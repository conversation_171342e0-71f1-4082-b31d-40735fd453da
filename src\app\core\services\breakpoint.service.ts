import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class BreakpointService {

  private readonly breakpointObserver = inject(BreakpointObserver);

  private readonly layoutChanges = toSignal(
    this.breakpointObserver
      .observe(Object.values(Breakpoints))
      .pipe(map(({ breakpoints }) => breakpoints)),
  );

  public readonly isHandset = computed(
    () => this.layoutChanges()?.[Breakpoints.Handset] ?? false,
  );

  public readonly isTablet = computed(
    () => this.layoutChanges()?.[Breakpoints.Tablet] ?? false,
  );

  public readonly isWebLandscape = computed(
    () => this.layoutChanges()?.[Breakpoints.WebLandscape] ?? false,
  );

  public readonly isLarge = computed(
    () => this.layoutChanges()?.[Breakpoints.Large] ?? false,
  );

  public readonly isMedium = computed(
    () => this.layoutChanges()?.[Breakpoints.Medium] ?? false,
  );

  public readonly isSmall = computed(
    () => this.layoutChanges()?.[Breakpoints.Small] ?? false,
  );

  public readonly isWeb = computed(
    () => this.layoutChanges()?.[Breakpoints.Web] ?? false,
  );

  public readonly isWebPortrait = computed(
    () => this.layoutChanges()?.[Breakpoints.WebPortrait] ?? false,
  );

  public readonly isXSmall = computed(
    () => this.layoutChanges()?.[Breakpoints.XSmall] ?? false,
  );

}
