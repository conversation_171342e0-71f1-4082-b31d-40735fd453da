{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"base-project-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "none", "changeDetection": "<PERSON><PERSON><PERSON>", "skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/base-project-angular", "index": "src/index.html", "browser": "src/main.ts", "polyfills": [], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets"], "styles": ["primeicons/primeicons.css", "primeflex/primeflex.css", "node_modules/leaflet/dist/leaflet.css", "src/assets/styles/custom-style.scss", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "base-project-angular:build:production"}, "development": {"buildTarget": "base-project-angular:build:development"}}, "options": {"proxyConfig": "proxy.config.json"}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:jest", "options": {"tsConfig": "tsconfig.spec.json"}}}}}, "cli": {"analytics": false}}