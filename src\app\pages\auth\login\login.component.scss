.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  // padding: 1rem;
  background-image: url('assets/images/ecobackground.png');
  background-size: cover;
}

.login-card {
  width: 100%;
  max-width: 400px;
  
  ::ng-deep .p-card {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: none;
    
    .p-card-header {
      padding: 2rem 2rem 1rem 2rem;
      border-bottom: none;
    }
    
    .p-card-body {
      padding: 0 2rem 2rem 2rem;
    }
  }
}

.login-header {
  text-align: center;
  
  .logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1rem;
    object-fit: cover;
  }
  
  h2 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-weight: 600;
    font-size: 1.5rem;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
  }
}

.form-field {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
  }
  
  input {
    width: 100%;
  }
  
  .p-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
  }
}

.form-actions {
  margin-top: 2rem;
  
  ::ng-deep .p-button {
    height: 3rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    
    &.p-button-loading {
      .p-button-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Responsividade
@media (max-width: 480px) {
  .login-container {
    padding: 0.5rem;
  }
  
  .login-card {
    ::ng-deep .p-card {
      .p-card-header {
        padding: 1.5rem 1.5rem 0.5rem 1.5rem;
      }
      
      .p-card-body {
        padding: 0 1.5rem 1.5rem 1.5rem;
      }
    }
  }
  
  .login-header {
    .logo {
      width: 60px;
      height: 60px;
    }
    
    h2 {
      font-size: 1.3rem;
    }
  }
}

// Customização do PrimeNG Password
::ng-deep .p-password {
  width: 100%;
  
  .p-password-input {
    width: 100%;
  }
}

// Customização dos inputs com erro
::ng-deep .ng-invalid.ng-touched {
  .p-inputtext,
  .p-password-input {
    border-color: #e24c4c;
    
    &:focus {
      border-color: #e24c4c;
      box-shadow: 0 0 0 0.2rem rgba(226, 76, 76, 0.25);
    }
  }
}
