import type { Routes } from '@angular/router';
import { authGuard, loginGuard } from '../core/auth/guards/auth.guard';
import { PagesComponent } from './pages.component';

export const PAGES_ROUTES: Routes = [
  {
    path: 'login',
    canActivate: [loginGuard],
    loadComponent: () =>
      import('./auth/login/login.component').then((c) => c.LoginComponent),
  },
  {
    path: '',
    component: PagesComponent,
    canActivate: [authGuard],
    children: [
      {
        path: '',
        redirectTo: '/containers',
        pathMatch: 'full'
      },
      {
        path: 'home',
        loadComponent: () =>
          import('./home/<USER>').then((c) => c.HomeComponent),
      },
      {
        path: 'containers',
        loadComponent: () =>
          import('./mapa/mapa.component').then((c) => c.MapaComponent),
      },
      {
        path: 'mapa',
        redirectTo: '/containers',
        pathMatch: 'full'
      },
    ],
  },
];
