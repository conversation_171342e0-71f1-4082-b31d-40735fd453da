{"name": "base-project-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "jest", "format": "biome format . --write", "lint": "biome check ."}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/google-maps": "^19.2.19", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@primeng/themes": "^19.1.3", "@primeuix/themes": "^1.2.3", "@types/leaflet": "^1.9.20", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primeng": "^19.1.4", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.2.15", "@angular/compiler-cli": "^19.2.0", "@biomejs/biome": "^2.0.0", "@types/jest": "^30.0.0", "jest": "^29.7.0", "jest-preset-angular": "^14.6.0", "typescript": "~5.7.2"}}