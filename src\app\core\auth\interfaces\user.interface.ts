export interface Usuario {
  id: string;
  nome: string;
  email: string;
  cargo?: string;
  ativo: boolean;
  filialAtual?: Filial;
  filiaisDisponiveis: Filial[];
}

export interface Filial {
  id: string;
  codigo: string;
  codigoFormatado: string;
  nome: string;
  endereco: string;
  bairro: string;
  cidade: string;
  uf: string;
  cep?: string;
  ativa: boolean;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  codigo: number;
  nome: string;
  situacao: string;
  email: string;
  empresas: Empresa[];
}

export interface Empresa {
  db: string;
  nivel: number;
  codPessoaDb: number;
}

export interface FilialResponse {
  id: string;
  codigo: string;
  codigoFormatado: string;
  nome: string;
  endereco: string;
  bairro: string;
  cidade: string;
  uf: string;
  cep?: string;
  ativa: boolean;
}
