.menu-lateral {
  width: 280px;
  min-width: 280px;
  height: 100vh;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  &.collapsed {
    width: 70px;
    min-width: 70px;
  }
}

.menu-header {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;

  .logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease-in-out;

    &.collapsed {
      justify-content: center;
    }

    .logo-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .logo-text {
      font-size: 1.25rem;
      font-weight: 600;
      white-space: nowrap;
    }
  }

  .icon-arrow {
    color: #6c757d !important;
  }

  .toggle-btn {
    ::ng-deep {
      .p-button {
        width: 32px;
        height: 32px;
        color: #6c757d;

        &:hover {
          background-color: #f8f9fa;
          color: #007bff;
        }
      }
    }
  }
}

.menu-nav {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .menu-section {
    margin-bottom: 1.5rem;

    .section-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      margin-bottom: 0.5rem;

      .section-icon {
        color: #6c757d;
        font-size: 0.875rem;
      }

      .section-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .section-divider {
      height: 1px;
      background-color: #e9ecef;
      margin: 0.5rem 1rem 1rem 1rem;
    }
  }

  .menu-item-container {
    margin-bottom: 0.5rem;

    ::ng-deep {
      .menu-item {
        width: 100%;
        padding: 0.75rem 0.5rem;
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
        color: #495057;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: start;

        .p-button-label {
          display: none;
        }

        .menu-label {
          // margin-left: 0.75rem;
          font-size: 0.875rem;
        }

        &:hover {
          background-color: #f8f9fa;
          color: #007bff;
          // transform: translateX(2px);
        }

        &.active {
          background-color: #e7f3ff;
          font-weight: 600;
          box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
            color: #495057;
            transform: none;
          }
        }

        .p-badge {
          font-size: 0.6rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }
}

.menu-footer {
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;

  .user-info {
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background-color: #e9ecef;
    }

    &.expanded {
      background-color: #e9ecef;
    }

    .user-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem;

      .user-basic {
        display: flex;
        flex-direction: column;
        min-width: 0;
        flex: 1;

        .user-name {
          font-weight: 600;
          color: #495057;
          font-size: 0.875rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .user-role {
          color: #6c757d;
          font-size: 0.75rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .pi {
        color: #6c757d;
        font-size: 0.75rem;
        transition: transform 0.3s ease;
      }
    }

    .user-expanded {
      padding: 0 0.5rem 0.5rem 0.5rem;
      border-top: 1px solid #dee2e6;
      margin-top: 0.5rem;
      animation: slideDown 0.3s ease;

      .user-email {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        padding: 0.5rem;
        background-color: #fff;
        border-radius: 6px;
        border: 1px solid #dee2e6;

        .pi {
          color: #6c757d;
          font-size: 0.75rem;
        }

        span {
          font-size: 0.75rem;
          color: #6c757d;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .filial-info {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background-color: #fff;
        border-radius: 6px;
        border: 1px solid #dee2e6;

        .filial-header {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.5rem;

          .pi {
            color: #28a745;
            font-size: 0.75rem;
          }

          .filial-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.75rem;
          }
        }

        .filial-details {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .filial-name {
            font-weight: 600;
            color: #28a745;
            font-size: 0.75rem;
          }

          .filial-address,
          .filial-city {
            color: #6c757d;
            font-size: 0.7rem;
            line-height: 1.2;
          }
        }
      }

      .user-actions {
        ::ng-deep .p-button {
          height: 2rem;
          font-size: 0.75rem;

          .p-button-label {
            font-weight: 500;
          }
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Customizações para PrimeNG
:host ::ng-deep {
  .p-button {
    border: none;
    background: transparent;
    box-shadow: none;

    &:enabled:hover {
      background-color: transparent;
    }

    &:focus {
      box-shadow: none;
    }
  }

  .p-avatar {
    flex-shrink: 0;
  }

  .p-badge {
    border-radius: 12px;
  }
}

// Responsividade
@media (max-width: 768px) {
  .menu-lateral {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);

    &.mobile-open {
      transform: translateX(0);
    }

    &.collapsed {
      width: 280px;
      min-width: 280px;
    }
  }
}

// Animações
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-label {
  animation: slideIn 0.3s ease-in-out;
}

:host ::ng-deep p-avatar {
  background-color: var(--p-green-700);
}
